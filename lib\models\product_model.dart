class ProductModel {
  final int id;
  final String judul;
  final String subJudul;
  final String deskripsi;
  final String gambar;
  final double rating;

  ProductModel({
    required this.id,
    required this.judul,
    required this.subJudul,
    required this.deskripsi,
    required this.gambar,
    required this.rating,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    return ProductModel(
      id: json['id'],
      judul: json['judul'],
      subJudul: json['subJudul'],
      deskripsi: json['deskripsi'],
      gambar: json['gambar'],
      rating: (json['rating'] as num).toDouble(),
    );
  }
}
