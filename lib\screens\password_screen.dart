import 'package:flutter/material.dart';
import '../utils/shared_pref_helper.dart';

class PasswordScreen extends StatefulWidget {
  const PasswordScreen({super.key});

  @override
  State<PasswordScreen> createState() => _PasswordScreenState();
}

class _PasswordScreenState extends State<PasswordScreen> {
  final _oldPass = TextEditingController();
  final _newPass = TextEditingController();
  final _confirmPass = TextEditingController();

  Future<void> _updatePassword() async {
    if (_newPass.text != _confirmPass.text) {
      showDialog(
        context: context,
        builder: (_) => const AlertDialog(
          title: Text("Error"),
          content: Text("Konfirmasi password tidak cocok"),
        ),
      );
      return;
    }

    final isValidOld = await SharedPrefHelper.validateOldPassword(_oldPass.text);
    if (!isValidOld) {
      showDialog(
        context: context,
        builder: (_) => const AlertDialog(
          title: Text("Error"),
          content: Text("Password lama salah"),
        ),
      );
      return;
    }

    await SharedPrefHelper.updatePassword(_newPass.text);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Ubah Password")),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            TextField(
              controller: _oldPass,
              decoration: const InputDecoration(labelText: "Password Lama"),
              obscureText: true,
            ),
            TextField(
              controller: _newPass,
              decoration: const InputDecoration(labelText: "Password Baru"),
              obscureText: true,
            ),
            TextField(
              controller: _confirmPass,
              decoration: const InputDecoration(labelText: "Konfirmasi Password"),
              obscureText: true,
            ),
            const SizedBox(height: 20),
            ElevatedButton(onPressed: _updatePassword, child: const Text("Update")),
          ],
        ),
      ),
    );
  }
}
