import 'package:shared_preferences/shared_preferences.dart';

class SharedPrefHelper {
  static const _usernameKey = 'username';
  static const _passwordKey = 'password';

  /// Set default credentials (admin / admin123) jika belum ada
  static Future<void> setDefaultCredentials() async {
    final prefs = await SharedPreferences.getInstance();

    if (!prefs.containsKey(_usernameKey)) {
      await prefs.setString(_usernameKey, 'admin');
    }

    if (!prefs.containsKey(_passwordKey)) {
      await prefs.setString(_passwordKey, 'admin123');
    }
  }

  /// Validasi login
  static Future<bool> validateLogin(String username, String password) async {
    final prefs = await SharedPreferences.getInstance();

    final savedUsername = prefs.getString(_usernameKey);
    final savedPassword = prefs.getString(_passwordKey);

    return username == savedUsername && password == savedPassword;
  }

  /// Validasi password lama saat ubah password
  static Future<bool> validateOldPassword(String oldPassword) async {
    final prefs = await SharedPreferences.getInstance();
    final savedPassword = prefs.getString(_passwordKey);
    return oldPassword == savedPassword;
  }

  /// Simpan password baru
  static Future<void> updatePassword(String newPassword) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_passwordKey, newPassword);
  }
}
